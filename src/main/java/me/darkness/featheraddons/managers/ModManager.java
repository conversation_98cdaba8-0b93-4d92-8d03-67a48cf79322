package me.darkness.featheraddons.managers;

import me.darkness.featheraddons.FeatherAddonsPlugin;
import org.bukkit.Bukkit;
import org.bukkit.entity.Player;

import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;

public class ModManager {

    private final FeatherAddonsPlugin plugin;
    private List<String> defaultBlockedMods;
    private boolean featherApiAvailable = false;

    public ModManager(FeatherAddonsPlugin plugin) {
        this.plugin = plugin;
        checkFeatherApiAvailability();
        loadConfiguration();
    }

    private void checkFeatherApiAvailability() {
        try {
            Class.forName("net.digitalingot.featherserverapi.FeatherAPI");
            featherApiAvailable = true;
            plugin.getMessageUtils().debugMessage("FeatherAPI is available");
        } catch (ClassNotFoundException e) {
            featherApiAvailable = false;
            plugin.getLogger().warning("FeatherAPI not found - mod management will be disabled");
        }
    }

    public void loadConfiguration() {
        this.defaultBlockedMods = new ArrayList<>(plugin.getConfigManager().getBlockedMods());
        plugin.getMessageUtils().debugMessage("Loaded %d default blocked mods", defaultBlockedMods.size());
    }

    public void reload() {
        checkFeatherApiAvailability();
        loadConfiguration();
    }

    public void applyDefaultBlockedMods(Object featherPlayer) {
        if (!featherApiAvailable || !plugin.getConfigManager().isAutoBlockOnJoin() || defaultBlockedMods.isEmpty()) {
            return;
        }

        try {
            // Use reflection to call FeatherAPI methods
            Class<?> featherModClass = Class.forName("net.digitalingot.featherserverapi.FeatherMod");
            List<Object> modsToBlock = new ArrayList<>();

            for (String modName : defaultBlockedMods) {
                Object featherMod = featherModClass.getConstructor(String.class).newInstance(modName);
                modsToBlock.add(featherMod);
            }

            Method blockModsMethod = featherPlayer.getClass().getMethod("blockMods", Collection.class);
            blockModsMethod.invoke(featherPlayer, modsToBlock);

            plugin.getMessageUtils().debugMessage("Applied %d default blocked mods to player", modsToBlock.size());

            if (plugin.getConfigManager().shouldNotifyPlayers()) {
                Method getUniqueIdMethod = featherPlayer.getClass().getMethod("getUniqueId");
                UUID playerUuid = (UUID) getUniqueIdMethod.invoke(featherPlayer);
                Player bukkitPlayer = Bukkit.getPlayer(playerUuid);
                if (bukkitPlayer != null) {
                    plugin.getMessageUtils().sendModsMessage(bukkitPlayer, "mods-blocked", defaultBlockedMods);
                }
            }
        } catch (Exception e) {
            plugin.getLogger().warning("Failed to apply default blocked mods: " + e.getMessage());
            if (plugin.getConfigManager().isDebugEnabled()) {
                e.printStackTrace();
            }
        }
    }

    public boolean blockMod(String modName, UUID playerUuid) {
        if (!featherApiAvailable) {
            return false;
        }

        try {
            Object featherPlayer = getFeatherPlayerByUuid(playerUuid);
            if (featherPlayer == null) {
                return false;
            }

            Class<?> featherModClass = Class.forName("net.digitalingot.featherserverapi.FeatherMod");
            Object mod = featherModClass.getConstructor(String.class).newInstance(modName);

            Method blockModsMethod = featherPlayer.getClass().getMethod("blockMods", Collection.class);
            blockModsMethod.invoke(featherPlayer, List.of(mod));

            plugin.getMessageUtils().debugMessage("Blocked mod '%s' for player", modName);
            return true;
        } catch (Exception e) {
            plugin.getLogger().warning("Failed to block mod: " + e.getMessage());
            return false;
        }
    }

    public boolean unblockMod(String modName, UUID playerUuid) {
        if (!featherApiAvailable) {
            return false;
        }

        try {
            Object featherPlayer = getFeatherPlayerByUuid(playerUuid);
            if (featherPlayer == null) {
                return false;
            }

            Class<?> featherModClass = Class.forName("net.digitalingot.featherserverapi.FeatherMod");
            Object mod = featherModClass.getConstructor(String.class).newInstance(modName);

            Method unblockModsMethod = featherPlayer.getClass().getMethod("unblockMods", Collection.class);
            unblockModsMethod.invoke(featherPlayer, List.of(mod));

            plugin.getMessageUtils().debugMessage("Unblocked mod '%s' for player", modName);
            return true;
        } catch (Exception e) {
            plugin.getLogger().warning("Failed to unblock mod: " + e.getMessage());
            return false;
        }
    }
    
    public boolean blockMods(List<String> modNames, UUID playerUuid) {
        if (!featherApiAvailable) {
            return false;
        }

        try {
            Object featherPlayer = getFeatherPlayerByUuid(playerUuid);
            if (featherPlayer == null) {
                return false;
            }

            Class<?> featherModClass = Class.forName("net.digitalingot.featherserverapi.FeatherMod");
            List<Object> mods = new ArrayList<>();

            for (String modName : modNames) {
                Object mod = featherModClass.getConstructor(String.class).newInstance(modName);
                mods.add(mod);
            }

            Method blockModsMethod = featherPlayer.getClass().getMethod("blockMods", Collection.class);
            blockModsMethod.invoke(featherPlayer, mods);

            plugin.getMessageUtils().debugMessage("Blocked %d mods for player", mods.size());
            return true;
        } catch (Exception e) {
            plugin.getLogger().warning("Failed to block mods: " + e.getMessage());
            return false;
        }
    }

    public boolean unblockMods(List<String> modNames, UUID playerUuid) {
        if (!featherApiAvailable) {
            return false;
        }

        try {
            Object featherPlayer = getFeatherPlayerByUuid(playerUuid);
            if (featherPlayer == null) {
                return false;
            }

            Class<?> featherModClass = Class.forName("net.digitalingot.featherserverapi.FeatherMod");
            List<Object> mods = new ArrayList<>();

            for (String modName : modNames) {
                Object mod = featherModClass.getConstructor(String.class).newInstance(modName);
                mods.add(mod);
            }

            Method unblockModsMethod = featherPlayer.getClass().getMethod("unblockMods", Collection.class);
            unblockModsMethod.invoke(featherPlayer, mods);

            plugin.getMessageUtils().debugMessage("Unblocked %d mods for player", mods.size());
            return true;
        } catch (Exception e) {
            plugin.getLogger().warning("Failed to unblock mods: " + e.getMessage());
            return false;
        }
    }
    
    public CompletableFuture<List<String>> getBlockedMods(UUID playerUuid) {
        if (!featherApiAvailable) {
            return CompletableFuture.completedFuture(new ArrayList<>());
        }

        // For now, return empty list - this would need more complex reflection
        return CompletableFuture.completedFuture(new ArrayList<>());
    }

    public CompletableFuture<List<String>> getEnabledMods(UUID playerUuid) {
        if (!featherApiAvailable) {
            return CompletableFuture.completedFuture(new ArrayList<>());
        }

        // For now, return empty list - this would need more complex reflection
        return CompletableFuture.completedFuture(new ArrayList<>());
    }

    public boolean isFeatherPlayer(UUID playerUuid) {
        return getFeatherPlayerByUuid(playerUuid) != null;
    }

    public Object getFeatherPlayer(UUID playerUuid) {
        return getFeatherPlayerByUuid(playerUuid);
    }

    public Collection<Object> getAllFeatherPlayers() {
        if (!featherApiAvailable) {
            return new ArrayList<>();
        }

        try {
            Class<?> featherApiClass = Class.forName("net.digitalingot.featherserverapi.FeatherAPI");
            Method getPlayerServiceMethod = featherApiClass.getMethod("getPlayerService");
            Object playerService = getPlayerServiceMethod.invoke(null);

            Method getPlayersMethod = playerService.getClass().getMethod("getPlayers");
            return (Collection<Object>) getPlayersMethod.invoke(playerService);
        } catch (Exception e) {
            plugin.getLogger().warning("Failed to get all FeatherPlayers: " + e.getMessage());
            return new ArrayList<>();
        }
    }
    
    public List<String> getDefaultBlockedMods() {
        return new ArrayList<>(defaultBlockedMods);
    }
    
    public boolean isValidModName(String modName) {
        // Basic validation - mod names should be lowercase and contain only letters, numbers, and underscores
        return modName != null && modName.matches("^[a-z0-9_]+$");
    }

    private Object getFeatherPlayerByUuid(UUID playerUuid) {
        if (!featherApiAvailable) {
            return null;
        }

        try {
            Class<?> featherApiClass = Class.forName("net.digitalingot.featherserverapi.FeatherAPI");
            Method getPlayerServiceMethod = featherApiClass.getMethod("getPlayerService");
            Object playerService = getPlayerServiceMethod.invoke(null);

            Method getPlayerMethod = playerService.getClass().getMethod("getPlayer", UUID.class);
            return getPlayerMethod.invoke(playerService, playerUuid);
        } catch (Exception e) {
            plugin.getLogger().warning("Failed to get FeatherPlayer: " + e.getMessage());
            return null;
        }
    }

    public boolean isFeatherApiAvailable() {
        return featherApiAvailable;
    }
}
