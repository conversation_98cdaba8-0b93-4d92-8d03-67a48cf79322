package me.darkness.featheraddons.managers;

import me.darkness.featheraddons.FeatherAddonsPlugin;
import net.digitalingot.feather.serverapi.api.FeatherAPI;
import net.digitalingot.feather.serverapi.api.player.FeatherPlayer;
import net.digitalingot.feather.serverapi.api.meta.DiscordActivity;
import org.bukkit.Bukkit;
import org.bukkit.scheduler.BukkitTask;

import java.util.Collection;

public class DiscordManager {

    private final FeatherAddonsPlugin plugin;
    private BukkitTask updateTask;
    private DiscordActivity defaultActivity;

    public DiscordManager(FeatherAddonsPlugin plugin) {
        this.plugin = plugin;
        loadConfiguration();
        startUpdateTask();
    }
    
    public void loadConfiguration() {
        if (!plugin.getConfigManager().isDiscordEnabled()) {
            plugin.getMessageUtils().debugMessage("Discord Rich Presence is disabled");
            return;
        }

        // Create default activity from config
        createDefaultActivity();

        plugin.getMessageUtils().debugMessage("Discord Rich Presence configuration loaded");
    }

    public void reload() {
        stopUpdateTask();
        loadConfiguration();
        startUpdateTask();
    }

    public void shutdown() {
        stopUpdateTask();
        clearAllActivities();
    }

    private void createDefaultActivity() {
        if (!plugin.getConfigManager().isDiscordEnabled()) {
            return;
        }

        DiscordActivity.Builder builder = DiscordActivity.builder();

        // Set server icon if provided (max 127 characters)
        String serverIcon = plugin.getConfigManager().getServerIcon();
        if (!serverIcon.isEmpty()) {
            if (serverIcon.length() > 127) {
                plugin.getLogger().warning("Server icon URL is too long (" + serverIcon.length() + " chars, max 127). Skipping icon.");
                plugin.getMessageUtils().debugMessage("Icon URL: %s", serverIcon);
            } else {
                builder.withImage(serverIcon);
            }
        }

        // Set server name
        String serverName = plugin.getConfigManager().getServerName();
        if (!serverName.isEmpty()) {
            builder.withImageText(serverName);
        }

        // Set details and state
        String details = plugin.getConfigManager().getDiscordDetails();
        if (!details.isEmpty()) {
            builder.withDetails(details);
        }

        String state = plugin.getConfigManager().getDiscordState();
        if (!state.isEmpty()) {
            builder.withState(state);
        }

        // Set party size if enabled
        if (plugin.getConfigManager().shouldShowPlayerCount()) {
            int onlinePlayers = Bukkit.getOnlinePlayers().size();
            int maxPlayers = plugin.getConfigManager().getMaxPlayers();

            if (maxPlayers <= 0) {
                maxPlayers = Bukkit.getMaxPlayers();
            }

            // Ensure party size is at least 1 (FeatherAPI requirement)
            if (onlinePlayers < 1) {
                onlinePlayers = 1;
            }
            if (maxPlayers < 1) {
                maxPlayers = 1;
            }

            builder.withPartySize(onlinePlayers, maxPlayers);
        }

        // Set start timestamp
        builder.withStartTimestamp(System.currentTimeMillis());

        this.defaultActivity = builder.build();

        plugin.getMessageUtils().debugMessage("Created default Discord activity");
    }
    
    private void startUpdateTask() {
        if (!plugin.getConfigManager().isDiscordEnabled()) {
            return;
        }
        
        int interval = plugin.getConfigManager().getUpdateInterval();
        if (interval <= 0) {
            plugin.getMessageUtils().debugMessage("Discord update interval is disabled");
            return;
        }
        
        updateTask = Bukkit.getScheduler().runTaskTimerAsynchronously(plugin, () -> {
            updateAllActivities();
        }, 20L * interval, 20L * interval); // Convert seconds to ticks
        
        plugin.getMessageUtils().debugMessage("Started Discord update task with interval: %d seconds", interval);
    }
    
    private void stopUpdateTask() {
        if (updateTask != null) {
            updateTask.cancel();
            updateTask = null;
            plugin.getMessageUtils().debugMessage("Stopped Discord update task");
        }
    }
    
    public void updateAllActivities() {
        if (!plugin.getConfigManager().isDiscordEnabled() || defaultActivity == null) {
            return;
        }
        
        // Recreate activity with current player count
        createDefaultActivity();
        
        Collection<FeatherPlayer> featherPlayers = FeatherAPI.getPlayerService().getPlayers();
        
        for (FeatherPlayer player : featherPlayers) {
            try {
                FeatherAPI.getMetaService().updateDiscordActivity(player, defaultActivity);
            } catch (Exception e) {
                plugin.getMessageUtils().debugMessage("Failed to update Discord activity for player %s: %s", 
                        player.getName(), e.getMessage());
            }
        }
        
        plugin.getMessageUtils().debugMessage("Updated Discord activity for %d players", featherPlayers.size());
    }
    
    public void updatePlayerActivity(FeatherPlayer player) {
        if (!plugin.getConfigManager().isDiscordEnabled() || defaultActivity == null) {
            return;
        }
        
        try {
            FeatherAPI.getMetaService().updateDiscordActivity(player, defaultActivity);
            plugin.getMessageUtils().debugMessage("Updated Discord activity for player %s", player.getName());
        } catch (Exception e) {
            plugin.getMessageUtils().debugMessage("Failed to update Discord activity for player %s: %s", 
                    player.getName(), e.getMessage());
        }
    }
    
    public void clearPlayerActivity(FeatherPlayer player) {
        try {
            FeatherAPI.getMetaService().clearDiscordActivity(player);
            plugin.getMessageUtils().debugMessage("Cleared Discord activity for player %s", player.getName());
        } catch (Exception e) {
            plugin.getMessageUtils().debugMessage("Failed to clear Discord activity for player %s: %s", 
                    player.getName(), e.getMessage());
        }
    }
    
    public void clearAllActivities() {
        Collection<FeatherPlayer> featherPlayers = FeatherAPI.getPlayerService().getPlayers();
        
        for (FeatherPlayer player : featherPlayers) {
            clearPlayerActivity(player);
        }
        
        plugin.getMessageUtils().debugMessage("Cleared Discord activity for all players");
    }
    
    public DiscordActivity createCustomActivity(String details, String state, String imageUrl, String imageText) {
        DiscordActivity.Builder builder = DiscordActivity.builder();
        
        if (details != null && !details.isEmpty()) {
            builder.withDetails(details);
        }
        
        if (state != null && !state.isEmpty()) {
            builder.withState(state);
        }
        
        if (imageUrl != null && !imageUrl.isEmpty()) {
            if (imageUrl.length() > 127) {
                plugin.getLogger().warning("Custom image URL is too long (" + imageUrl.length() + " chars, max 127). Skipping image.");
            } else {
                builder.withImage(imageUrl);
            }
        }
        
        if (imageText != null && !imageText.isEmpty()) {
            builder.withImageText(imageText);
        }
        
        // Add party size if enabled
        if (plugin.getConfigManager().shouldShowPlayerCount()) {
            int onlinePlayers = Bukkit.getOnlinePlayers().size();
            int maxPlayers = plugin.getConfigManager().getMaxPlayers();

            if (maxPlayers <= 0) {
                maxPlayers = Bukkit.getMaxPlayers();
            }

            // Ensure party size is at least 1 (FeatherAPI requirement)
            if (onlinePlayers < 1) {
                onlinePlayers = 1;
            }
            if (maxPlayers < 1) {
                maxPlayers = 1;
            }

            builder.withPartySize(onlinePlayers, maxPlayers);
        }
        
        builder.withStartTimestamp(System.currentTimeMillis());
        
        return builder.build();
    }

    public boolean isEnabled() {
        return plugin.getConfigManager().isDiscordEnabled();
    }

    public DiscordActivity getDefaultActivity() {
        return defaultActivity;
    }
}
