package me.darkness.featheraddons.managers;

import me.darkness.featheraddons.FeatherAddonsPlugin;
import net.digitalingot.feather.serverapi.api.FeatherAPI;
import net.digitalingot.feather.serverapi.api.meta.ServerListBackground;
import net.digitalingot.feather.serverapi.api.meta.ServerListBackgroundFactory;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;

public class ServerBackgroundManager {
    
    private final FeatherAddonsPlugin plugin;
    private ServerListBackground currentBackground;
    
    public ServerBackgroundManager(FeatherAddonsPlugin plugin) {
        this.plugin = plugin;
    }
    
    public void reload() {
        if (plugin.getConfigManager().isServerBackgroundEnabled() && 
            plugin.getConfigManager().shouldLoadOnStartup()) {
            loadBackground();
        }
    }
    
    public void loadBackground() {
        if (!plugin.getConfigManager().isServerBackgroundEnabled()) {
            plugin.getMessageUtils().debugMessage("Server background is disabled");
            return;
        }

        String imageFile = plugin.getConfigManager().getBackgroundImageFile();
        if (imageFile == null || imageFile.isEmpty()) {
            plugin.getLogger().warning("No background image file specified in config");
            return;
        }

        File backgroundsDir = new File(plugin.getDataFolder(), "backgrounds");
        File imageFileObj = new File(backgroundsDir, imageFile);

        plugin.getLogger().info("Looking for background image: " + imageFileObj.getAbsolutePath());

        if (!imageFileObj.exists()) {
            plugin.getLogger().warning("Background image file not found: " + imageFileObj.getPath());
            plugin.getLogger().info("Please place your background image in: " + backgroundsDir.getPath());
            plugin.getLogger().info("Requirements: PNG format, max 1009x202 pixels, max 512KB");
            plugin.getLogger().info("Current config setting: image-file: \"" + imageFile + "\"");

            // List files in backgrounds directory
            File[] files = backgroundsDir.listFiles();
            if (files != null && files.length > 0) {
                plugin.getLogger().info("Files found in backgrounds directory:");
                for (File file : files) {
                    plugin.getLogger().info("  - " + file.getName() + " (" + file.length() + " bytes)");
                }
            } else {
                plugin.getLogger().info("Backgrounds directory is empty");
            }
            return;
        }

        try {
            loadBackgroundFromFile(imageFileObj.toPath());
            plugin.getLogger().info("Server background loaded successfully: " + imageFile);
        } catch (Exception e) {
            plugin.getLogger().severe("Failed to load server background: " + e.getMessage());
            if (plugin.getConfigManager().isDebugEnabled()) {
                e.printStackTrace();
            }
        }
    }
    
    private void loadBackgroundFromFile(Path imagePath) throws IOException {
        // Validate file size before loading
        long fileSize = Files.size(imagePath);
        long maxSize = 512 * 1024; // 512KB in bytes

        if (fileSize > maxSize) {
            throw new RuntimeException("Image file is too large: " + fileSize + " bytes (max: " + maxSize + " bytes)");
        }
        
        plugin.getMessageUtils().debugMessage("Loading background image: %s (size: %d bytes)", 
                imagePath.getFileName(), fileSize);
        
        try {
            ServerListBackgroundFactory factory = FeatherAPI.getMetaService().getServerListBackgroundFactory();
            ServerListBackground background = factory.byPath(imagePath);
            
            FeatherAPI.getMetaService().setServerListBackground(background);
            this.currentBackground = background;
            
            plugin.getMessageUtils().debugMessage("Successfully set server background");

        } catch (Exception e) {
            throw new RuntimeException("Failed to set server background: " + e.getMessage(), e);
        }
    }
    
    public boolean setBackground(String fileName) {
        if (!plugin.getConfigManager().isServerBackgroundEnabled()) {
            return false;
        }
        
        File backgroundsDir = new File(plugin.getDataFolder(), "backgrounds");
        File imageFile = new File(backgroundsDir, fileName);
        
        if (!imageFile.exists()) {
            plugin.getLogger().warning("Background image file not found: " + imageFile.getPath());
            return false;
        }
        
        try {
            loadBackgroundFromFile(imageFile.toPath());
            
            // Update config
            plugin.getConfigManager().setConfigValue("server-background.image-file", fileName);
            
            plugin.getLogger().info("Server background changed to: " + fileName);
            return true;
            
        } catch (Exception e) {
            plugin.getLogger().severe("Failed to set server background: " + e.getMessage());
            return false;
        }
    }
    
    public boolean removeBackground() {
        try {
            // Clear the background
            FeatherAPI.getMetaService().setServerListBackground(null);
            this.currentBackground = null;
            
            plugin.getLogger().info("Server background removed");
            return true;
            
        } catch (Exception e) {
            plugin.getLogger().severe("Failed to remove server background: " + e.getMessage());
            return false;
        }
    }
    
    public ServerListBackground getCurrentBackground() {
        return currentBackground;
    }
    
    public boolean hasBackground() {
        return currentBackground != null;
    }
    
    public void createBackgroundsDirectory() {
        File backgroundsDir = new File(plugin.getDataFolder(), "backgrounds");
        if (!backgroundsDir.exists()) {
            backgroundsDir.mkdirs();
            plugin.getLogger().info("Created backgrounds directory: " + backgroundsDir.getPath());
            
            // Create a README file with instructions
            createReadmeFile(backgroundsDir);
        }
    }
    
    private void createReadmeFile(File backgroundsDir) {
        File readmeFile = new File(backgroundsDir, "README.txt");
        if (!readmeFile.exists()) {
            try {
                String content = "777-FeatherAddons Background Images\n" +
                        "=====================================\n\n" +
                        "Place your server background images in this folder.\n\n" +
                        "Requirements:\n" +
                        "- Format: PNG only\n" +
                        "- Recommended dimensions: 909x102 pixels\n" +
                        "- Maximum dimensions: 1009x202 pixels\n" +
                        "- Maximum file size: 512KB\n\n" +
                        "To use a background:\n" +
                        "1. Place your PNG file in this folder\n" +
                        "2. Update the 'image-file' setting in config.yml\n" +
                        "3. Reload the plugin with /777featheraddons reload\n\n" +
                        "Example config.yml setting:\n" +
                        "server-background:\n" +
                        "  image-file: \"my-background.png\"\n";
                
                Files.write(readmeFile.toPath(), content.getBytes());
                plugin.getMessageUtils().debugMessage("Created README file in backgrounds directory");
                
            } catch (IOException e) {
                plugin.getLogger().warning("Failed to create README file: " + e.getMessage());
            }
        }
    }
    
    public String getBackgroundInfo() {
        if (!hasBackground()) {
            return "No background currently set";
        }
        
        String fileName = plugin.getConfigManager().getBackgroundImageFile();
        File backgroundFile = new File(new File(plugin.getDataFolder(), "backgrounds"), fileName);
        
        if (backgroundFile.exists()) {
            long fileSize = backgroundFile.length();
            return String.format("Current background: %s (%.1f KB)", fileName, fileSize / 1024.0);
        } else {
            return "Background set but file not found: " + fileName;
        }
    }
}
