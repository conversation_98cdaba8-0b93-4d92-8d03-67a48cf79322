package me.darkness.featheraddons.listeners;

import me.darkness.featheraddons.FeatherAddonsPlugin;
import net.digitalingot.feather.serverapi.api.FeatherAPI;
import net.digitalingot.feather.serverapi.api.player.FeatherPlayer;
import net.digitalingot.feather.serverapi.api.event.player.PlayerHelloEvent;
import org.bukkit.Bukkit;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.Listener;
import org.bukkit.event.player.PlayerQuitEvent;

public class FeatherPlayerListener implements Listener {
    
    private final FeatherAddonsPlugin plugin;
    
    public FeatherPlayerListener(FeatherAddonsPlugin plugin) {
        this.plugin = plugin;
        
        // Subscribe to Feather events
        subscribeToFeatherEvents();
    }
    
    private void subscribeToFeatherEvents() {
        // Subscribe to PlayerHelloEvent
        FeatherAPI.getEventService().subscribe(PlayerHelloEvent.class, this::onPlayerHello);
        
        plugin.getMessageUtils().debugMessage("Subscribed to Feather events");
    }
    
    private void onPlayerHello(PlayerHelloEvent event) {
        FeatherPlayer featherPlayer = event.getPlayer();
        Player bukkitPlayer = Bukkit.getPlayer(featherPlayer.getUniqueId());
        
        if (bukkitPlayer == null) {
            return;
        }
        
        plugin.getMessageUtils().debugMessage("Feather player joined: %s (Platform: %s, Mods: %d)", 
                featherPlayer.getName(), 
                event.getPlatform().name(), 
                event.getFeatherMods().size());
        
        // Apply default blocked mods
        Bukkit.getScheduler().runTaskLater(plugin, () -> {
            plugin.getModManager().applyDefaultBlockedMods(featherPlayer);
        }, 20L); // Wait 1 second before applying mods
        
        // Update Discord Rich Presence
        if (plugin.getDiscordManager().isEnabled()) {
            Bukkit.getScheduler().runTaskLater(plugin, () -> {
                plugin.getDiscordManager().updatePlayerActivity(featherPlayer);
            }, 40L); // Wait 2 seconds before updating Discord
        }
        
        // Log mod information if debug is enabled
        if (plugin.getConfigManager().isDebugEnabled()) {
            event.getFeatherMods().forEach(mod -> {
                plugin.getMessageUtils().debugMessage("Player %s has mod: %s", 
                        featherPlayer.getName(), mod.getName());
            });
        }
    }
    
    @EventHandler(priority = EventPriority.MONITOR)
    public void onPlayerQuit(PlayerQuitEvent event) {
        Player player = event.getPlayer();
        FeatherPlayer featherPlayer = plugin.getModManager().getFeatherPlayer(player.getUniqueId());
        
        if (featherPlayer != null) {
            plugin.getMessageUtils().debugMessage("Feather player quit: %s", player.getName());
            
            // Clear Discord activity when player leaves
            if (plugin.getDiscordManager().isEnabled()) {
                plugin.getDiscordManager().clearPlayerActivity(featherPlayer);
            }
        }
    }
}
