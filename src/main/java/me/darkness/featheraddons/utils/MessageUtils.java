package me.darkness.featheraddons.utils;

import me.darkness.featheraddons.FeatherAddonsPlugin;
import org.bukkit.ChatColor;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.Player;

import java.util.Collection;

public class MessageUtils {
    
    private final FeatherAddonsPlugin plugin;
    
    public MessageUtils(FeatherAddonsPlugin plugin) {
        this.plugin = plugin;
    }
    
    public String colorize(String message) {
        return ChatColor.translateAlternateColorCodes('&', message);
    }
    
    public String getFormattedMessage(String key) {
        String prefix = plugin.getConfigManager().getPrefix();
        String message = plugin.getConfigManager().getMessage(key);
        return colorize(prefix + message);
    }
    
    public String getFormattedMessage(String key, String placeholder, String value) {
        String message = getFormattedMessage(key);
        return message.replace("{" + placeholder + "}", value);
    }
    
    public void sendMessage(CommandSender sender, String key) {
        sender.sendMessage(getFormattedMessage(key));
    }
    
    public void sendMessage(CommandSender sender, String key, String placeholder, String value) {
        sender.sendMessage(getFormattedMessage(key, placeholder, value));
    }
    
    public void sendRawMessage(CommandSender sender, String message) {
        sender.sendMessage(colorize(message));
    }
    
    public void sendPrefixedMessage(CommandSender sender, String message) {
        String prefix = plugin.getConfigManager().getPrefix();
        sender.sendMessage(colorize(prefix + message));
    }
    
    public void sendModsMessage(CommandSender sender, String key, Collection<String> mods) {
        String modsList = String.join(", ", mods);
        sendMessage(sender, key, "mods", modsList);
    }
    
    public void sendHelpMessage(CommandSender sender) {
        sendMessage(sender, "help-header");
        sendMessage(sender, "help-main");
        sendMessage(sender, "help-reload");
        sendMessage(sender, "help-mods");
        sendMessage(sender, "help-footer");
    }
    
    public void debugMessage(String message) {
        if (plugin.getConfigManager().isDebugEnabled()) {
            plugin.getLogger().info("[DEBUG] " + message);
        }
    }
    
    public void debugMessage(String message, Object... args) {
        if (plugin.getConfigManager().isDebugEnabled()) {
            plugin.getLogger().info("[DEBUG] " + String.format(message, args));
        }
    }
}
