package me.darkness.featheraddons.configuration;

import me.darkness.featheraddons.FeatherAddonsPlugin;
import org.bukkit.configuration.file.FileConfiguration;

import java.util.List;

public class ConfigManager {
    
    private final FeatherAddonsPlugin plugin;
    private FileConfiguration config;
    
    public ConfigManager(FeatherAddonsPlugin plugin) {
        this.plugin = plugin;
    }
    
    public void loadConfiguration() {
        this.config = plugin.getConfig();
        
        // Validate configuration
        validateConfiguration();
    }
    
    private void validateConfiguration() {
        // Check if required sections exist
        if (!config.contains("mods")) {
            plugin.getLogger().warning("Missing 'mods' section in config.yml");
        }
        
        if (!config.contains("discord")) {
            plugin.getLogger().warning("Missing 'discord' section in config.yml");
        }
        
        if (!config.contains("server-background")) {
            plugin.getLogger().warning("Missing 'server-background' section in config.yml");
        }
    }
    
    // Mod Configuration
    public List<String> getBlockedMods() {
        return config.getStringList("mods.blocked");
    }
    
    public boolean isAutoBlockOnJoin() {
        return config.getBoolean("mods.auto-block-on-join", true);
    }
    
    public boolean shouldNotifyPlayers() {
        return config.getBoolean("mods.notify-players", true);
    }
    
    // Discord Configuration
    public boolean isDiscordEnabled() {
        return config.getBoolean("discord.enabled", true);
    }
    
    public String getServerIcon() {
        return config.getString("discord.server-icon", "");
    }
    
    public String getServerName() {
        return config.getString("discord.server-name", "777 Server");
    }
    
    public String getDiscordDetails() {
        return config.getString("discord.details", "Playing on 777 Server");
    }
    
    public String getDiscordState() {
        return config.getString("discord.state", "Survival Mode");
    }
    
    public boolean shouldShowPlayerCount() {
        return config.getBoolean("discord.show-player-count", true);
    }
    
    public int getMaxPlayers() {
        return config.getInt("discord.max-players", 0);
    }
    
    public int getUpdateInterval() {
        return config.getInt("discord.update-interval", 30);
    }
    
    // Server Background Configuration
    public boolean isServerBackgroundEnabled() {
        return config.getBoolean("server-background.enabled", true);
    }
    
    public String getBackgroundImageFile() {
        return config.getString("server-background.image-file", "server-background.png");
    }
    
    public boolean shouldLoadOnStartup() {
        return config.getBoolean("server-background.load-on-startup", true);
    }
    
    // General Configuration
    public boolean isDebugEnabled() {
        return config.getBoolean("general.debug", false);
    }
    
    public String getLanguage() {
        return config.getString("general.language", "en");
    }
    
    public boolean shouldCheckUpdates() {
        return config.getBoolean("general.check-updates", true);
    }
    
    // Message Configuration
    public String getPrefix() {
        return config.getString("messages.prefix", "&8[&6777&eFeatherAddons&8]&r ");
    }
    
    public String getMessage(String key) {
        return config.getString("messages." + key, "Message not found: " + key);
    }
    
    public String getMessage(String key, String defaultValue) {
        return config.getString("messages." + key, defaultValue);
    }
    
    // Utility methods
    public void setConfigValue(String path, Object value) {
        config.set(path, value);
        plugin.saveConfig();
    }
    
    public FileConfiguration getConfig() {
        return config;
    }
}
