package me.darkness.featheraddons.commands.subcommands;

import me.darkness.featheraddons.FeatherAddonsPlugin;
import me.darkness.featheraddons.commands.SubCommand;
import org.bukkit.Bukkit;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.Player;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

public class ModsSubCommand implements SubCommand {
    
    private final FeatherAddonsPlugin plugin;
    private final List<String> commonMods = Arrays.asList(
            "perspective", "zoom", "timer", "fps", "coordinates", "keystrokes",
            "cps", "ping", "armor_status", "item_physics", "motion_blur",
            "brightness", "saturation", "crosshair", "waypoints", "minimap"
    );
    
    public ModsSubCommand(FeatherAddonsPlugin plugin) {
        this.plugin = plugin;
    }
    
    @Override
    public String getName() {
        return "mods";
    }
    
    @Override
    public String getDescription() {
        return "Manage Feather mods for players";
    }
    
    @Override
    public String getUsage() {
        return "/777featheraddons mods <block|unblock> <mod> [player]";
    }
    
    @Override
    public String getPermission() {
        return "featheraddons.mods";
    }
    
    @Override
    public List<String> getAliases() {
        return Arrays.asList("mod", "m");
    }
    
    @Override
    public void execute(CommandSender sender, String[] args) {
        if (args.length < 2) {
            plugin.getMessageUtils().sendPrefixedMessage(sender, "&cUsage: " + getUsage());
            return;
        }
        
        String action = args[0].toLowerCase();
        String modName = args[1].toLowerCase();
        
        // Validate mod name
        if (!plugin.getModManager().isValidModName(modName)) {
            plugin.getMessageUtils().sendMessage(sender, "mod-not-found", "mod", modName);
            return;
        }
        
        // Determine target player
        Player targetPlayer = null;
        if (args.length >= 3) {
            // Target specified
            targetPlayer = Bukkit.getPlayer(args[2]);
            if (targetPlayer == null) {
                plugin.getMessageUtils().sendPrefixedMessage(sender, "&cPlayer not found: " + args[2]);
                return;
            }
        } else {
            // No target specified, use sender if they're a player
            if (sender instanceof Player) {
                targetPlayer = (Player) sender;
            } else {
                plugin.getMessageUtils().sendMessage(sender, "player-only");
                return;
            }
        }
        
        UUID playerUuid = targetPlayer.getUniqueId();
        
        // Check if player is using Feather
        if (!plugin.getModManager().isFeatherPlayer(playerUuid)) {
            plugin.getMessageUtils().sendMessage(sender, "no-feather-player");
            return;
        }
        
        // Execute action
        switch (action) {
            case "block":
                executeBlockAction(sender, modName, playerUuid, targetPlayer.getName());
                break;
            case "unblock":
                executeUnblockAction(sender, modName, playerUuid, targetPlayer.getName());
                break;
            default:
                plugin.getMessageUtils().sendPrefixedMessage(sender, "&cInvalid action. Use 'block' or 'unblock'");
                break;
        }
    }
    
    private void executeBlockAction(CommandSender sender, String modName, UUID playerUuid, String playerName) {
        boolean success = plugin.getModManager().blockMod(modName, playerUuid);
        
        if (success) {
            plugin.getMessageUtils().sendPrefixedMessage(sender, 
                    "&aBlocked mod '&e" + modName + "&a' for player &e" + playerName);
            
            // Notify target player if different from sender
            Player targetPlayer = Bukkit.getPlayer(playerUuid);
            if (targetPlayer != null && !targetPlayer.equals(sender)) {
                plugin.getMessageUtils().sendModsMessage(targetPlayer, "mods-blocked", Arrays.asList(modName));
            }
        } else {
            plugin.getMessageUtils().sendPrefixedMessage(sender, "&cFailed to block mod");
        }
    }
    
    private void executeUnblockAction(CommandSender sender, String modName, UUID playerUuid, String playerName) {
        boolean success = plugin.getModManager().unblockMod(modName, playerUuid);
        
        if (success) {
            plugin.getMessageUtils().sendPrefixedMessage(sender, 
                    "&aUnblocked mod '&e" + modName + "&a' for player &e" + playerName);
            
            // Notify target player if different from sender
            Player targetPlayer = Bukkit.getPlayer(playerUuid);
            if (targetPlayer != null && !targetPlayer.equals(sender)) {
                plugin.getMessageUtils().sendModsMessage(targetPlayer, "mods-unblocked", Arrays.asList(modName));
            }
        } else {
            plugin.getMessageUtils().sendPrefixedMessage(sender, "&cFailed to unblock mod");
        }
    }
    
    @Override
    public List<String> getTabCompletions(CommandSender sender, String[] args) {
        List<String> completions = new ArrayList<>();
        
        if (args.length == 1) {
            // Complete action
            completions.addAll(Arrays.asList("block", "unblock"));
        } else if (args.length == 2) {
            // Complete mod name
            String input = args[1].toLowerCase();
            completions.addAll(commonMods.stream()
                    .filter(mod -> mod.startsWith(input))
                    .collect(Collectors.toList()));
        } else if (args.length == 3) {
            // Complete player name
            String input = args[2].toLowerCase();
            completions.addAll(Bukkit.getOnlinePlayers().stream()
                    .map(Player::getName)
                    .filter(name -> name.toLowerCase().startsWith(input))
                    .collect(Collectors.toList()));
        }
        
        return completions;
    }
}
