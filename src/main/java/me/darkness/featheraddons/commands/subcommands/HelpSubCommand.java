package me.darkness.featheraddons.commands.subcommands;

import me.darkness.featheraddons.FeatherAddonsPlugin;
import me.darkness.featheraddons.commands.SubCommand;
import org.bukkit.command.CommandSender;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public class HelpSubCommand implements SubCommand {
    
    private final FeatherAddonsPlugin plugin;
    
    public HelpSubCommand(FeatherAddonsPlugin plugin) {
        this.plugin = plugin;
    }
    
    @Override
    public String getName() {
        return "help";
    }
    
    @Override
    public String getDescription() {
        return "Show help information";
    }
    
    @Override
    public String getUsage() {
        return "/777featheraddons help";
    }
    
    @Override
    public String getPermission() {
        return "featheraddons.use";
    }
    
    @Override
    public List<String> getAliases() {
        return Arrays.asList("?", "h");
    }
    
    @Override
    public void execute(CommandSender sender, String[] args) {
        plugin.getMessageUtils().sendHelpMessage(sender);
    }
    
    @Override
    public List<String> getTabCompletions(CommandSender sender, String[] args) {
        return new ArrayList<>();
    }
}
