# 777-FeatherAddons Configuration
# Plugin for managing Feather client features

# Mod Management Settings
mods:
  # Default blocked mods (applied to all players)
  # Use mod names from: https://docs.feathermc.com/server-api/mods
  blocked:
    - "perspective"
    - "zoom"
    - "timer"
  
  # Auto-block mods for new players
  auto-block-on-join: true
  
  # Send notification when mods are blocked/unblocked
  notify-players: true

# Discord Rich Presence Configuration
discord:
  enabled: true
  
  # Server icon URL (must be accessible via HTTP/HTTPS)
  # Recommended size: 512x512 pixels
  server-icon: "https://media.discordapp.net/attachments/1389938206362832896/1398057235858456717/image.png?ex=688b3a03&is=6889e883&hm=bb180cb4bf2fef8427a3df2c429e86758d84ef09fcbc04fec95f9f4942ba67be&=&format=webp&quality=lossless&width=742&height=742"
  
  # Text shown when hovering over the server icon
  server-name: "test1"
  
  # Main activity text (larger text)
  details: "test2"
  
  # Secondary activity text (smaller text)
  state: "test3"
  
  # Show player count in party size
  show-player-count: true
  
  # Maximum players to show (0 = use server max players)
  max-players: 0
  
  # Update interval in seconds
  update-interval: 30

# Server List Background Configuration
server-background:
  enabled: true
  
  # Background image file name (must be in plugins/777-FeatherAddons/backgrounds/ folder)
  # Requirements:
  # - Format: PNG only
  # - Recommended dimensions: 909x102 pixels
  # - Maximum dimensions: 1009x202 pixels
  # - Maximum file size: 512KB
  image-file: "banner.png"
  
  # Load background on server startup
  load-on-startup: true

# General Settings
general:
  # Enable debug messages
  debug: false
  
  # Language (currently only 'en' supported)
  language: "en"
  
  # Check for updates on startup
  check-updates: true

# Messages Configuration
messages:
  prefix: "&8[&6777&eFeatherAddons&8]&r "
  
  # Command messages
  no-permission: "&cYou don't have permission to use this command!"
  player-only: "&cThis command can only be used by players!"
  invalid-command: "&cInvalid command! Use /777featheraddons help"
  config-reloaded: "&aConfiguration reloaded successfully!"
  
  # Mod management messages
  mods-blocked: "&aMods blocked: &e{mods}"
  mods-unblocked: "&aMods unblocked: &e{mods}"
  mod-not-found: "&cMod '{mod}' not found!"
  no-feather-player: "&cPlayer is not using Feather client!"
  
  # Discord messages
  discord-updated: "&aDiscord Rich Presence updated!"
  discord-disabled: "&cDiscord Rich Presence is disabled in config!"
  
  # Background messages
  background-loaded: "&aServer background loaded successfully!"
  background-error: "&cError loading server background: {error}"
  background-disabled: "&cServer background is disabled in config!"
  
  # Help messages
  help-header: "&6=== 777-FeatherAddons Help ==="
  help-main: "&e/777featheraddons help &7- Show this help"
  help-reload: "&e/777featheraddons reload &7- Reload configuration"
  help-mods: "&e/777featheraddons mods <block|unblock> <mod> [player] &7- Manage mods"
  help-footer: "&6=========================="
