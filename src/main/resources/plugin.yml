name: 777-FeatherAddons
version: '1.0'
main: me.darkness.featheraddons.FeatherAddonsPlugin
api-version: '1.18'
depend: [FeatherServerAPI]
author: darkness
description: Advanced Feather client management plugin

commands:
  777featheraddons:
    description: Main command for 777-FeatherAddons
    usage: /777featheraddons <subcommand>
    aliases: [777fa, featheraddons]
    permission: featheraddons.use

permissions:
  featheraddons.*:
    description: All FeatherAddons permissions
    default: op
    children:
      featheraddons.use: true
      featheraddons.admin: true
      featheraddons.mods: true
      featheraddons.reload: true
  featheraddons.use:
    description: Basic FeatherAddons usage
    default: true
  featheraddons.admin:
    description: Administrative commands
    default: op
  featheraddons.mods:
    description: Manage mods
    default: op
  featheraddons.reload:
    description: Reload configuration
    default: op
